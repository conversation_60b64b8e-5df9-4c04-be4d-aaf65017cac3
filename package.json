{"name": "mysql-", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "electron-builder", "preview": "vite preview"}, "build": {"appId": "com.example.app", "win": {"icon": "public/snake-icon.svg", "target": [{"target": "nsis", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "perMachine": true, "allowToChangeInstallationDirectory": true}}, "dependencies": {"bcryptjs": "^3.0.2", "jsonwebtoken": "^9.0.2", "mysql2": "^3.14.2", "node-ssh": "^13.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "ssh2": "^1.16.0"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.1.0", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "electron": "^30.0.1", "electron-builder": "^24.13.3", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.1.6", "vite-plugin-electron": "^0.28.6", "vite-plugin-electron-renderer": "^0.14.5"}, "main": "dist-electron/main.js"}