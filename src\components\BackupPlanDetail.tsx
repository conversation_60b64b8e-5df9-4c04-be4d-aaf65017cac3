import React, { useState, useEffect } from 'react';
import './BackupPlanDetail.css';

interface BackupHistoryItem {
  id: number;
  backup_type: 'full' | 'incremental';
  status: 'running' | 'completed' | 'failed';
  start_time: string;
  end_time?: string;
  file_size: number;
  file_path: string;
  error_message?: string;
  binlog_file?: string;
  binlog_position?: number;
  duration?: number;
  compression_ratio?: number;
  records_count?: number;
}

interface BackupTask {
  id: number;
  name: string;
  database_name: string;
  server_name?: string;
  schedule_type: string;
  schedule_time?: string;
  status: string;
  created_at: string;
  last_backup_time?: string;
  total_backups?: number;
  success_rate?: number;
}

interface BackupStats {
  totalBackups: number;
  successfulBackups: number;
  failedBackups: number;
  totalSize: number;
  avgDuration: number;
  lastFullBackup?: string;
  nextScheduledBackup?: string;
}

interface BackupPlanDetailProps {
  taskId: number;
  userId: number;
  onBack: () => void;
}

const BackupPlanDetail: React.FC<BackupPlanDetailProps> = ({ taskId, userId, onBack }) => {
  const [task, setTask] = useState<BackupTask | null>(null);
  const [history, setHistory] = useState<BackupHistoryItem[]>([]);
  const [stats, setStats] = useState<BackupStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [generatingSQL, setGeneratingSQL] = useState<number | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d');
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    loadTaskHistory();

    // 自动刷新
    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(() => {
        loadTaskHistory(true);
      }, 30000); // 每30秒刷新一次
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [taskId, autoRefresh]);

  const loadTaskHistory = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!window.electronAPI) {
        throw new Error('electronAPI 不可用');
      }

      const result = await window.electronAPI.getBackupTaskHistory(taskId, userId);
      
      if (result.success) {
        setTask(result.task || null);
        setHistory(result.history || []);
      } else {
        setError(result.message || '获取备份历史失败');
      }
    } catch (err) {
      console.error('获取备份历史失败:', err);
      setError('获取备份历史失败');
    } finally {
      setLoading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (startTime: string, endTime?: string): string => {
    if (!endTime) return '-';
    const start = new Date(startTime);
    const end = new Date(endTime);
    const duration = end.getTime() - start.getTime();
    const minutes = Math.floor(duration / 60000);
    const seconds = Math.floor((duration % 60000) / 1000);
    return `${minutes}分${seconds}秒`;
  };

  const handleDownloadCompleteSQL = async (historyId: number) => {
    try {
      setGeneratingSQL(historyId);

      if (!window.electronAPI) {
        throw new Error('electronAPI 不可用');
      }

      const result = await window.electronAPI.generateCompleteBackupSQL(
        taskId,
        historyId,
        userId
      );

      if (result.success) {
        // 显示生成成功的信息
        alert(`完整SQL文件生成成功！\n文件名: ${result.fileName}\n文件大小: ${formatFileSize(result.fileSize || 0)}\n包含增量备份: ${result.incrementalBackups?.length || 0} 个`);
        
        // 可以在这里添加下载逻辑
        if (result.filePath && window.electronAPI.showItemInFolder) {
          await window.electronAPI.showItemInFolder(result.filePath);
        }
      } else {
        alert(`生成失败: ${result.message}`);
      }
    } catch (err) {
      console.error('生成完整SQL文件失败:', err);
      alert('生成完整SQL文件失败');
    } finally {
      setGeneratingSQL(null);
    }
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'completed': return '#28a745';
      case 'running': return '#007bff';
      case 'failed': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'completed': return '已完成';
      case 'running': return '运行中';
      case 'failed': return '失败';
      default: return '未知';
    }
  };

  if (loading) {
    return (
      <div className="backup-plan-detail">
        <div className="loading">
          <div className="loading-spinner"></div>
          <p>加载中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="backup-plan-detail">
        <div className="error-message">
          <h3>错误</h3>
          <p>{error}</p>
          <button onClick={onBack} className="btn btn-primary">
            返回备份管理
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="backup-plan-detail">
      <div className="detail-header">
        <button onClick={onBack} className="btn btn-secondary">
          ← 返回
        </button>
        <div className="task-info">
          <h2>{task?.name || '备份计划详情'}</h2>
          <p>数据库: {task?.database_name}</p>
        </div>
        <button onClick={loadTaskHistory} className="btn btn-primary">
          刷新
        </button>
      </div>

      <div className="history-timeline">
        <h3>备份历史时间线</h3>
        {history.length === 0 ? (
          <div className="no-history">
            <p>暂无备份历史记录</p>
          </div>
        ) : (
          <div className="timeline">
            {history.map((item, index) => (
              <div key={item.id} className="timeline-item">
                <div className="timeline-marker" style={{ backgroundColor: getStatusColor(item.status) }}>
                  <span className="timeline-number">{index + 1}</span>
                </div>
                <div className="timeline-content">
                  <div className="timeline-header">
                    <h4>
                      {item.backup_type === 'full' ? '完整备份' : '增量备份'}
                      <span className="status-badge" style={{ backgroundColor: getStatusColor(item.status) }}>
                        {getStatusText(item.status)}
                      </span>
                    </h4>
                    <div className="timeline-actions">
                      {item.status === 'completed' && (
                        <button
                          onClick={() => handleDownloadCompleteSQL(item.id)}
                          disabled={generatingSQL === item.id}
                          className="btn btn-sm btn-outline"
                        >
                          {generatingSQL === item.id ? '生成中...' : '下载完整SQL'}
                        </button>
                      )}
                    </div>
                  </div>
                  <div className="timeline-details">
                    <div className="detail-row">
                      <span>开始时间:</span>
                      <span>{new Date(item.start_time).toLocaleString()}</span>
                    </div>
                    {item.end_time && (
                      <div className="detail-row">
                        <span>结束时间:</span>
                        <span>{new Date(item.end_time).toLocaleString()}</span>
                      </div>
                    )}
                    <div className="detail-row">
                      <span>持续时间:</span>
                      <span>{formatDuration(item.start_time, item.end_time)}</span>
                    </div>
                    <div className="detail-row">
                      <span>文件大小:</span>
                      <span>{formatFileSize(item.file_size)}</span>
                    </div>
                    {item.error_message && (
                      <div className="detail-row error">
                        <span>错误信息:</span>
                        <span>{item.error_message}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default BackupPlanDetail;
