export interface ServerData {
  userId: number;
  name: string;
  host: string;
  port: number;
  username: string;
  password: string;
  sshHost?: string;
  sshPort?: number;
  sshUsername?: string;
  sshPassword?: string;
  sshPrivateKey?: string;
}

export interface BackupTaskData {
  userId: number;
  serverId: number;
  name: string;
  databaseName: string;
  backupType: 'full' | 'incremental';
  scheduleType: 'manual' | 'hourly' | 'daily' | 'weekly' | 'monthly';
  scheduleTime?: string;
  scheduleDay?: number;
  backupPath: string;
  retentionDays: number;
}

export interface ElectronAPI {
  userRegister: (userData: { username: string; email: string; password: string }) => Promise<{
    success: boolean;
    message: string;
    userId?: number;
  }>;

  userLogin: (credentials: { username: string; password: string }) => Promise<{
    success: boolean;
    message: string;
    token?: string;
    user?: {
      id: number;
      username: string;
      email: string;
    };
  }>;

  // 服务器管理API
  addServer: (serverData: ServerData) => Promise<{
    success: boolean;
    message: string;
    serverId?: number;
  }>;

  getServers: (userId: number) => Promise<{
    success: boolean;
    servers?: any[];
    message?: string;
  }>;

  deleteServer: (serverId: number, userId: number) => Promise<{
    success: boolean;
    message: string;
  }>;

  getServer: (serverId: number, userId: number) => Promise<{
    success: boolean;
    server?: any;
    message?: string;
  }>;

  updateServer: (serverId: number, userId: number, serverData: ServerData) => Promise<{
    success: boolean;
    message: string;
  }>;

  testServerConnection: (serverConfig: {
    host: string;
    port: number;
    username: string;
    password: string;
    sshHost?: string;
    sshPort?: number;
    sshUsername?: string;
    sshPassword?: string;
    sshPrivateKey?: string;
  }) => Promise<{
    success: boolean;
    message: string;
    details?: any;
  }>;

  updateServerStatus: (serverId: number, status: 'active' | 'inactive' | 'error') => Promise<{
    success: boolean;
    message?: string;
  }>;

  // 备份任务管理API
  createBackupTask: (taskData: BackupTaskData) => Promise<{
    success: boolean;
    message: string;
    taskId?: number;
  }>;

  getBackupTasks: (userId: number) => Promise<{
    success: boolean;
    tasks?: any[];
    message?: string;
  }>;

  deleteBackupTask: (taskId: number, userId: number) => Promise<{
    success: boolean;
    message: string;
  }>;

  // 备份历史API
  getBackupHistory: (userId: number, limit?: number) => Promise<{
    success: boolean;
    history?: any[];
    message?: string;
  }>;

  getBackupTaskHistory: (taskId: number, userId: number) => Promise<{
    success: boolean;
    task?: {
      id: number;
      name: string;
      database_name: string;
    };
    history?: any[];
    message?: string;
  }>;

  generateCompleteBackupSQL: (taskId: number, targetHistoryId: number, userId: number) => Promise<{
    success: boolean;
    message: string;
    filePath?: string;
    fileName?: string;
    fileSize?: number;
    fullBackup?: {
      id: number;
      start_time: string;
      file_size: number;
    };
    incrementalBackups?: Array<{
      id: number;
      start_time: string;
      file_size: number;
    }>;
  }>;

  executeBackup: (taskId: number) => Promise<{
    success: boolean;
    message?: string;
    filePath?: string;
    fileSize?: number;
  }>;

  getBackupFilePath: (historyId: number, userId: number) => Promise<{
    success: boolean;
    message?: string;
    filePath?: string;
  }>;

  showItemInFolder: (filePath: string) => Promise<{
    success: boolean;
    message?: string;
  }>;

  // 连接测试API
  testDatabaseExists: (serverConfig: {
    host: string;
    port: number;
    username: string;
    password: string;
    sshHost?: string;
    sshPort?: number;
    sshUsername?: string;
    sshPassword?: string;
    sshPrivateKey?: string;
  }, databaseName: string) => Promise<{
    success: boolean;
    message: string;
    details?: any;
  }>;

  testDatabaseConnection: (serverId: number, databaseName: string, userId: number) => Promise<{
    success: boolean;
    message?: string;
  }>;

  // 系统信息API
  getSystemInfo: (userId: number) => Promise<{
    success: boolean;
    data?: {
      productName: string;
      version: string;
      edition: string;
      licenseTo: string;
      installDate: Date;
      lastUpdateCheck: Date;
      platform: string;
      arch: string;
      hostname: string;
      uptime: number;
      startTime: Date;
    };
    message?: string;
  }>;

  getSystemMetrics: () => Promise<{
    success: boolean;
    data?: {
      timestamp: Date;
      cpu: {
        usage: number;
        cores: number;
        loadAverage: number[];
      };
      memory: {
        total: number;
        used: number;
        free: number;
        usage: number;
      };
      uptime: number;
    };
    message?: string;
  }>;

  getBackupStats: (userId: number) => Promise<{
    success: boolean;
    data?: {
      timestamp: Date;
      connectedDatabases: number;
      activeBackupJobs: number;
      totalBackups: number;
      completedBackups24h: number;
      failedBackups24h: number;
      totalBackupSize: number;
      averageBackupDuration: number;
      latestBackup?: {
        time: string;
        status: string;
        serverName: string;
        databaseName: string;
      };
    };
    message?: string;
  }>;

  // 服务器端备份文件API
  getServerBackupFiles: (serverId: number, userId: number) => Promise<{
    success: boolean;
    files?: Array<{
      path: string;
      name: string;
      size: number;
      modifiedTime: Date;
      backupPath: string;
    }>;
    message?: string;
  }>;

  downloadServerBackupFile: (serverId: number, filePath: string, userId: number) => Promise<{
    success: boolean;
    content?: string;
    fileName?: string;
    message?: string;
  }>;

  on: (channel: string, listener: (...args: any[]) => void) => void;
  off: (channel: string, ...args: any[]) => void;
  send: (channel: string, ...args: any[]) => void;
  invoke: (channel: string, ...args: any[]) => Promise<any>;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}
